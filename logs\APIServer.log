2025-06-12 00:08:41 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:08:41 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 00:08:45 - APIServer - INFO - API服务器已停止
2025-06-12 00:08:46 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:08:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:08:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:08:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:08:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:08:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:08:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:13:41 - APIServer - INFO - API服务器已停止
2025-06-12 13:15:22 - APIServer - INFO - CORS已配置为允许局域网访问
2025-06-12 13:15:23 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 13:15:23 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 13:15:30 - APIServer - INFO - API服务器已停止
2025-06-12 13:15:30 - APIServer - INFO - CORS已配置为允许局域网访问
2025-06-12 13:15:30 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 13:16:26 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 13:16:26 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 1, 'name': '示例图片', 'path': 'C:/shared/images', 'description': '包含示例图片文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T11:54:08.825361', 'updated_at': '2025-06-12T03:54:08', 'last_scanned': None, 'file_count': 2}, {'id': 2, 'name': '设计文件', 'path': 'C:/shared/design', 'description': '包含设计文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T11:54:08.826360', 'updated_at': '2025-06-12T03:54:08', 'last_scanned': None, 'file_count': 2}, {'id': 3, 'name': '照片集', 'path': 'C:/shared/photos', 'description': '包含照片的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T11:54:08.826360', 'updated_at': '2025-06-12T03:54:08', 'last_scanned': None, 'file_count': 1}]}
2025-06-12 13:16:26 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 13:16:26 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 13:16:26 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 1, 'name': '示例图片', 'path': 'C:/shared/images', 'description': '包含示例图片文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T11:54:08.825361', 'updated_at': '2025-06-12T03:54:08', 'last_scanned': None, 'file_count': 2}, {'id': 2, 'name': '设计文件', 'path': 'C:/shared/design', 'description': '包含设计文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T11:54:08.826360', 'updated_at': '2025-06-12T03:54:08', 'last_scanned': None, 'file_count': 2}, {'id': 3, 'name': '照片集', 'path': 'C:/shared/photos', 'description': '包含照片的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T11:54:08.826360', 'updated_at': '2025-06-12T03:54:08', 'last_scanned': None, 'file_count': 1}]}
2025-06-12 13:16:26 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 13:16:33 - APIServer - INFO - 获取用户 1 的下载记录，页码: 1, 限制: 50
2025-06-12 13:16:33 - APIServer - ERROR - 获取下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (1, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 13:16:33 - APIServer - INFO - 获取用户 1 的下载记录，页码: 1, 限制: 50
2025-06-12 13:16:33 - APIServer - ERROR - 获取下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (1, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
