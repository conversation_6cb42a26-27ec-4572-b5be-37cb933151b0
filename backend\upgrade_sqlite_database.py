#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite数据库升级脚本 - 修复下载记录表结构
"""

import sys
import os
import sqlite3
from datetime import datetime

def upgrade_download_records_table():
    """升级download_records表结构"""
    try:
        db_path = "../data/file_share_system.db"
        if not os.path.exists(db_path):
            print("数据库文件不存在，无需升级")
            return True
            
        print("正在升级SQLite数据库...")
        connection = sqlite3.connect(db_path)
        cursor = connection.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(download_records)")
        existing_columns = {row[1]: row[2] for row in cursor.fetchall()}
        print(f"当前download_records表有 {len(existing_columns)} 个字段")
        
        # 需要添加的字段
        new_columns = [
            ('batch_id', 'INTEGER'),
            ('session_id', 'TEXT'),
            ('ip_address', 'TEXT'),
            ('user_agent', 'TEXT'),
            ('download_source', 'TEXT'),
            ('downloaded_at', 'TEXT')
        ]
        
        # 添加缺失的字段
        for col_name, col_type in new_columns:
            if col_name not in existing_columns:
                try:
                    if col_name == 'download_source':
                        cursor.execute(f"ALTER TABLE download_records ADD COLUMN {col_name} {col_type} DEFAULT 'web'")
                    else:
                        cursor.execute(f"ALTER TABLE download_records ADD COLUMN {col_name} {col_type}")
                    print(f"✅ 添加字段: {col_name}")
                except Exception as e:
                    print(f"❌ 添加字段 {col_name} 失败: {e}")
            else:
                print(f"⏭️  字段已存在: {col_name}")
        
        # 检查download_batches表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='download_batches'")
        if not cursor.fetchone():
            print("创建download_batches表...")
            cursor.execute("""
                CREATE TABLE download_batches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    batch_id TEXT UNIQUE NOT NULL,
                    user_id INTEGER,
                    session_id TEXT,
                    batch_type TEXT NOT NULL,
                    target_type TEXT NOT NULL,
                    target_id INTEGER,
                    total_files INTEGER DEFAULT 0,
                    total_size INTEGER DEFAULT 0,
                    compressed_size INTEGER DEFAULT 0,
                    download_name TEXT NOT NULL,
                    is_encrypted INTEGER DEFAULT 0,
                    password_required INTEGER DEFAULT 0,
                    ip_address TEXT,
                    user_agent TEXT,
                    referer TEXT,
                    download_source TEXT DEFAULT 'web',
                    status TEXT DEFAULT 'preparing',
                    error_message TEXT,
                    created_at TEXT DEFAULT (datetime('now')),
                    started_at TEXT,
                    completed_at TEXT,
                    expires_at TEXT,
                    extra_data TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
                )
            """)
            print("✅ download_batches表创建成功")
        else:
            print("⏭️  download_batches表已存在")
        
        # 检查user_download_activities表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_download_activities'")
        if not cursor.fetchone():
            print("创建user_download_activities表...")
            cursor.execute("""
                CREATE TABLE user_download_activities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    batch_id INTEGER NOT NULL,
                    activity_type TEXT NOT NULL,
                    file_id INTEGER,
                    folder_id INTEGER,
                    file_name TEXT,
                    file_size INTEGER DEFAULT 0,
                    download_status TEXT DEFAULT 'pending',
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at TEXT DEFAULT (datetime('now')),
                    completed_at TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (batch_id) REFERENCES download_batches(id) ON DELETE CASCADE,
                    FOREIGN KEY (file_id) REFERENCES shared_files(id) ON DELETE SET NULL,
                    FOREIGN KEY (folder_id) REFERENCES shared_folders(id) ON DELETE SET NULL
                )
            """)
            print("✅ user_download_activities表创建成功")
        else:
            print("⏭️  user_download_activities表已存在")
        
        # 创建索引
        print("创建索引...")
        indexes = [
            ("idx_download_batch", "download_records", "batch_id"),
            ("idx_download_user_time", "download_records", "user_id, downloaded_at"),
            ("idx_batch_user", "download_batches", "user_id, created_at"),
            ("idx_batch_status", "download_batches", "status, created_at"),
            ("idx_activity_user", "user_download_activities", "user_id, created_at"),
            ("idx_activity_batch", "user_download_activities", "batch_id, created_at")
        ]
        
        for idx_name, table_name, columns in indexes:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {idx_name} ON {table_name} ({columns})")
                print(f"✅ 创建索引: {idx_name}")
            except Exception as e:
                print(f"❌ 创建索引 {idx_name} 失败: {e}")
        
        connection.commit()
        connection.close()
        
        print("\n🎉 数据库升级完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库升级失败: {e}")
        return False

def main():
    """主函数"""
    print("SQLite数据库升级工具")
    print("=" * 50)
    
    if upgrade_download_records_table():
        print("\n✓ 数据库升级成功")
        return 0
    else:
        print("\n✗ 数据库升级失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
